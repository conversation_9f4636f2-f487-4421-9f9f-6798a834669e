import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkBookReadAccess } from '@/lib/utils/book-access'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ bookId: string }> }
) {
  try {
    const { bookId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const postId = searchParams.get('postId')

    if (!postId) {
      return NextResponse.json({ error: 'postId is required' }, { status: 400 })
    }

    // Check if user has access to this book
    const accessResult = await checkBookReadAccess(supabase, user.id, bookId)
    if (!accessResult.hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Verify the post belongs to this book
    const { data: post, error: postError } = await supabase
      .from('book_audio_posts')
      .select('id')
      .eq('id', postId)
      .eq('project_id', bookId)
      .single()

    if (postError || !post) {
      return NextResponse.json({ error: 'Post not found' }, { status: 404 })
    }

    // Get replies for this post (including nested replies)
    const { data: replies, error } = await supabase
      .from('book_audio_replies')
      .select(`
        id,
        audio_url,
        duration_seconds,
        love_count,
        parent_reply_id,
        reply_number,
        created_at,
        reaction_counts,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number,
          badge_tier
        )
      `)
      .eq('book_audio_post_id', postId)
      .order('reply_number', { ascending: true })

    if (error) {
      console.error('Error fetching book audio replies:', error)
      return NextResponse.json({ error: 'Failed to fetch replies' }, { status: 500 })
    }

    return NextResponse.json({ replies: replies || [] })
  } catch (error) {
    console.error('Error in book audio replies API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ bookId: string }> }
) {
  try {
    const { bookId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has access to this book
    const accessResult = await checkBookReadAccess(supabase, user.id, bookId)
    if (!accessResult.hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const body = await request.json()
    const { postId, audioUrl, audioKey, duration, parentReplyId } = body

    // Validate required fields
    if (!postId || !audioUrl || !audioKey || !duration) {
      return NextResponse.json({ 
        error: 'Missing required fields: postId, audioUrl, audioKey, duration' 
      }, { status: 400 })
    }

    if (duration > 9.0) {
      return NextResponse.json({ 
        error: 'Duration must be 9 seconds or less' 
      }, { status: 400 })
    }

    // Verify the post belongs to this book
    const { data: post, error: postError } = await supabase
      .from('book_audio_posts')
      .select('id')
      .eq('id', postId)
      .eq('project_id', bookId)
      .single()

    if (postError || !post) {
      return NextResponse.json({ error: 'Post not found' }, { status: 404 })
    }

    // Validate parent reply if provided
    if (parentReplyId) {
      const { data: parentReply, error: parentError } = await supabase
        .from('book_audio_replies')
        .select('id, book_audio_post_id')
        .eq('id', parentReplyId)
        .eq('book_audio_post_id', postId)
        .single()

      if (parentError || !parentReply) {
        return NextResponse.json({ error: 'Parent reply not found' }, { status: 404 })
      }
    }

    // Create book audio reply
    const { data: reply, error } = await supabase
      .from('book_audio_replies')
      .insert({
        user_id: user.id,
        book_audio_post_id: postId,
        parent_reply_id: parentReplyId || null,
        audio_url: audioUrl,
        audio_key: audioKey,
        duration_seconds: duration,
        file_size_bytes: 0 // Default value
      })
      .select(`
        id,
        audio_url,
        duration_seconds,
        love_count,
        parent_reply_id,
        reply_number,
        created_at,
        reaction_counts,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number,
          badge_tier
        )
      `)
      .single()

    if (error) {
      console.error('Error creating book audio reply:', error)
      return NextResponse.json({ error: 'Failed to create reply' }, { status: 500 })
    }

    return NextResponse.json({ reply })
  } catch (error) {
    console.error('Error in book audio replies API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
